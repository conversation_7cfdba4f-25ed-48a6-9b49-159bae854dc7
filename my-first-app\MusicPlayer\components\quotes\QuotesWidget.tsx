import React from 'react';
import { StyleSheet, View, Share, Alert } from 'react-native';
import { ThemedView } from '@/components/ThemedView';
import { QuoteDisplay } from './QuoteDisplay';
import { QuoteControls } from './QuoteControls';
import { useQuotes } from '@/hooks/useQuotes';

export function QuotesWidget() {
  const {
    currentQuote,
    isAutoRotating,
    autoRotateInterval,
    isLoading,
    getNextQuote,
    getPreviousQuote,
    toggleFavorite,
    isFavorite,
    toggleAutoRotate,
    setAutoRotateInterval,
  } = useQuotes();

  const handleShare = async () => {
    if (!currentQuote) return;

    try {
      const message = `"${currentQuote.text}"\n\n— ${currentQuote.author}`;
      await Share.share({
        message,
        title: 'Inspirational Quote',
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to share quote');
    }
  };

  const handleToggleFavorite = () => {
    if (currentQuote) {
      toggleFavorite(currentQuote);
    }
  };

  if (isLoading || !currentQuote) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.loadingContainer}>
          {/* You could add a loading spinner here */}
        </View>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <View style={styles.quoteContainer}>
        <QuoteDisplay
          quote={currentQuote}
          isFavorite={isFavorite(currentQuote)}
          onToggleFavorite={handleToggleFavorite}
          onShare={handleShare}
        />
      </View>

      <QuoteControls
        isAutoRotating={isAutoRotating}
        autoRotateInterval={autoRotateInterval}
        onToggleAutoRotate={toggleAutoRotate}
        onPrevious={getPreviousQuote}
        onNext={getNextQuote}
        onIntervalChange={setAutoRotateInterval}
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  quoteContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
