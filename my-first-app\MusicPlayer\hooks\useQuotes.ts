import { useState, useEffect, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Quote, QuoteCategory, getRandomQuote, getQuotesByCategory, searchQuotes } from '@/data/quotes';

const FAVORITES_STORAGE_KEY = '@music_player_favorite_quotes';
const CURRENT_QUOTE_STORAGE_KEY = '@music_player_current_quote';
const AUTO_ROTATE_INTERVAL_KEY = '@music_player_auto_rotate_interval';

export interface QuotesState {
  currentQuote: Quote | null;
  favoriteQuotes: Quote[];
  isAutoRotating: boolean;
  autoRotateInterval: number; // in seconds
  isLoading: boolean;
}

export const useQuotes = () => {
  const [state, setState] = useState<QuotesState>({
    currentQuote: null,
    favoriteQuotes: [],
    isAutoRotating: false,
    autoRotateInterval: 30, // 30 seconds default
    isLoading: true,
  });

  // Load saved data on mount
  useEffect(() => {
    loadSavedData();
  }, []);

  // Auto-rotate timer
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (state.isAutoRotating && state.autoRotateInterval > 0) {
      interval = setInterval(() => {
        getNextQuote();
      }, state.autoRotateInterval * 1000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [state.isAutoRotating, state.autoRotateInterval]);

  const loadSavedData = async () => {
    try {
      const [favoritesData, currentQuoteData, intervalData] = await Promise.all([
        AsyncStorage.getItem(FAVORITES_STORAGE_KEY),
        AsyncStorage.getItem(CURRENT_QUOTE_STORAGE_KEY),
        AsyncStorage.getItem(AUTO_ROTATE_INTERVAL_KEY),
      ]);

      const favoriteQuotes = favoritesData ? JSON.parse(favoritesData) : [];
      const currentQuote = currentQuoteData ? JSON.parse(currentQuoteData) : getRandomQuote();
      const autoRotateInterval = intervalData ? parseInt(intervalData, 10) : 30;

      setState(prev => ({
        ...prev,
        favoriteQuotes,
        currentQuote,
        autoRotateInterval,
        isLoading: false,
      }));
    } catch (error) {
      console.error('Error loading saved quotes data:', error);
      setState(prev => ({
        ...prev,
        currentQuote: getRandomQuote(),
        isLoading: false,
      }));
    }
  };

  const saveFavorites = async (favorites: Quote[]) => {
    try {
      await AsyncStorage.setItem(FAVORITES_STORAGE_KEY, JSON.stringify(favorites));
    } catch (error) {
      console.error('Error saving favorites:', error);
    }
  };

  const saveCurrentQuote = async (quote: Quote) => {
    try {
      await AsyncStorage.setItem(CURRENT_QUOTE_STORAGE_KEY, JSON.stringify(quote));
    } catch (error) {
      console.error('Error saving current quote:', error);
    }
  };

  const saveAutoRotateInterval = async (interval: number) => {
    try {
      await AsyncStorage.setItem(AUTO_ROTATE_INTERVAL_KEY, interval.toString());
    } catch (error) {
      console.error('Error saving auto-rotate interval:', error);
    }
  };

  const getNextQuote = useCallback(() => {
    const nextQuote = getRandomQuote(state.currentQuote?.id);
    setState(prev => ({ ...prev, currentQuote: nextQuote }));
    saveCurrentQuote(nextQuote);
  }, [state.currentQuote?.id]);

  const getPreviousQuote = useCallback(() => {
    // For simplicity, just get a random quote (in a real app, you might maintain history)
    const prevQuote = getRandomQuote(state.currentQuote?.id);
    setState(prev => ({ ...prev, currentQuote: prevQuote }));
    saveCurrentQuote(prevQuote);
  }, [state.currentQuote?.id]);

  const toggleFavorite = useCallback(async (quote: Quote) => {
    const isFavorite = state.favoriteQuotes.some(fav => fav.id === quote.id);
    let newFavorites: Quote[];

    if (isFavorite) {
      newFavorites = state.favoriteQuotes.filter(fav => fav.id !== quote.id);
    } else {
      newFavorites = [...state.favoriteQuotes, quote];
    }

    setState(prev => ({ ...prev, favoriteQuotes: newFavorites }));
    await saveFavorites(newFavorites);
  }, [state.favoriteQuotes]);

  const isFavorite = useCallback((quote: Quote) => {
    return state.favoriteQuotes.some(fav => fav.id === quote.id);
  }, [state.favoriteQuotes]);

  const toggleAutoRotate = useCallback(() => {
    setState(prev => ({ ...prev, isAutoRotating: !prev.isAutoRotating }));
  }, []);

  const setAutoRotateInterval = useCallback(async (interval: number) => {
    setState(prev => ({ ...prev, autoRotateInterval: interval }));
    await saveAutoRotateInterval(interval);
  }, []);

  const getQuotesByCategory = useCallback((category: QuoteCategory) => {
    return getQuotesByCategory(category);
  }, []);

  const searchQuotesText = useCallback((searchTerm: string) => {
    return searchQuotes(searchTerm);
  }, []);

  const setCurrentQuote = useCallback(async (quote: Quote) => {
    setState(prev => ({ ...prev, currentQuote: quote }));
    await saveCurrentQuote(quote);
  }, []);

  return {
    ...state,
    getNextQuote,
    getPreviousQuote,
    toggleFavorite,
    isFavorite,
    toggleAutoRotate,
    setAutoRotateInterval,
    getQuotesByCategory,
    searchQuotes: searchQuotesText,
    setCurrentQuote,
  };
};
