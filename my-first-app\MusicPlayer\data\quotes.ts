export interface Quote {
  id: string;
  text: string;
  author: string;
  category: QuoteCategory;
  isFavorite?: boolean;
}

export type QuoteCategory =
  | 'motivational'
  | 'literary'
  | 'philosophical'
  | 'wisdom'
  | 'love'
  | 'success'
  | 'life'
  | 'music'
  | 'art'
  | 'science';

export const quotes: Quote[] = [
  {
    id: '1',
    text: 'Music is the universal language of mankind.',
    author: '<PERSON>',
    category: 'music'
  },
  {
    id: '2',
    text: 'Where words fail, music speaks.',
    author: '<PERSON>',
    category: 'music'
  },
  {
    id: '3',
    text: 'Music can heal the wounds which medicine cannot touch.',
    author: '<PERSON><PERSON><PERSON>',
    category: 'music'
  },
  {
    id: '4',
    text: 'The only way to do great work is to love what you do.',
    author: '<PERSON>',
    category: 'motivational'
  },
  {
    id: '5',
    text: 'Life is what happens to you while you\'re busy making other plans.',
    author: '<PERSON>',
    category: 'life'
  },
  {
    id: '6',
    text: 'The future belongs to those who believe in the beauty of their dreams.',
    author: '<PERSON>',
    category: 'motivational'
  },
  {
    id: '7',
    text: 'It is during our darkest moments that we must focus to see the light.',
    author: '<PERSON>',
    category: 'wisdom'
  },
  {
    id: '8',
    text: 'The way to get started is to quit talking and begin doing.',
    author: '<PERSON> <PERSON>',
    category: 'motivational'
  },
  {
    id: '9',
    text: 'Don\'t let yesterday take up too much of today.',
    author: 'Will Rogers',
    category: 'wisdom'
  },
  {
    id: '10',
    text: 'You learn more from failure than from success.',
    author: 'Unknown',
    category: 'wisdom'
  },
  {
    id: '11',
    text: 'If you are working on something that you really care about, you don\'t have to be pushed.',
    author: 'Steve Jobs',
    category: 'motivational'
  },
  {
    id: '12',
    text: 'Experience is the teacher of all things.',
    author: 'Julius Caesar',
    category: 'wisdom'
  },
  {
    id: '13',
    text: 'The best time to plant a tree was 20 years ago. The second best time is now.',
    author: 'Chinese Proverb',
    category: 'wisdom'
  },
  {
    id: '14',
    text: 'Your limitation—it\'s only your imagination.',
    author: 'Unknown',
    category: 'motivational'
  },
  {
    id: '15',
    text: 'Push yourself, because no one else is going to do it for you.',
    author: 'Unknown',
    category: 'motivational'
  },
  {
    id: '16',
    text: 'Great things never come from comfort zones.',
    author: 'Unknown',
    category: 'motivational'
  },
  {
    id: '17',
    text: 'Dream it. Wish it. Do it.',
    author: 'Unknown',
    category: 'motivational'
  },
  {
    id: '18',
    text: 'Success doesn\'t just find you. You have to go out and get it.',
    author: 'Unknown',
    category: 'success'
  },
  {
    id: '19',
    text: 'The harder you work for something, the greater you\'ll feel when you achieve it.',
    author: 'Unknown',
    category: 'success'
  },
  {
    id: '20',
    text: 'Dream bigger. Do bigger.',
    author: 'Unknown',
    category: 'motivational'
  },
  {
    id: '21',
    text: 'Don\'t stop when you\'re tired. Stop when you\'re done.',
    author: 'Unknown',
    category: 'motivational'
  },
  {
    id: '22',
    text: 'Wake up with determination. Go to bed with satisfaction.',
    author: 'Unknown',
    category: 'motivational'
  },
  {
    id: '23',
    text: 'Do something today that your future self will thank you for.',
    author: 'Sean Patrick Flanery',
    category: 'motivational'
  },
  {
    id: '24',
    text: 'Little things make big days.',
    author: 'Unknown',
    category: 'life'
  },
  {
    id: '25',
    text: 'It\'s going to be hard, but hard does not mean impossible.',
    author: 'Unknown',
    category: 'motivational'
  },
  {
    id: '26',
    text: 'Don\'t wait for opportunity. Create it.',
    author: 'Unknown',
    category: 'motivational'
  },
  {
    id: '27',
    text: 'Sometimes we\'re tested not to show our weaknesses, but to discover our strengths.',
    author: 'Unknown',
    category: 'wisdom'
  },
  {
    id: '28',
    text: 'The key to success is to focus on goals, not obstacles.',
    author: 'Unknown',
    category: 'success'
  },
  {
    id: '29',
    text: 'Dream it. Believe it. Build it.',
    author: 'Unknown',
    category: 'motivational'
  },
  {
    id: '30',
    text: 'What we think, we become.',
    author: 'Buddha',
    category: 'philosophical'
  },
  {
    id: '31',
    text: 'The mind is everything. What you think you become.',
    author: 'Buddha',
    category: 'philosophical'
  },
  {
    id: '32',
    text: 'Be yourself; everyone else is already taken.',
    author: 'Oscar Wilde',
    category: 'wisdom'
  },
  {
    id: '33',
    text: 'Two things are infinite: the universe and human stupidity; and I\'m not sure about the universe.',
    author: 'Albert Einstein',
    category: 'science'
  },
  {
    id: '34',
    text: 'So many books, so little time.',
    author: 'Frank Zappa',
    category: 'literary'
  },
  {
    id: '35',
    text: 'A room without books is like a body without a soul.',
    author: 'Marcus Tullius Cicero',
    category: 'literary'
  },
  {
    id: '36',
    text: 'You know you\'re in love when you can\'t fall asleep because reality is finally better than your dreams.',
    author: 'Dr. Seuss',
    category: 'love'
  },
  {
    id: '37',
    text: 'Be the change that you wish to see in the world.',
    author: 'Mahatma Gandhi',
    category: 'wisdom'
  },
  {
    id: '38',
    text: 'In three words I can sum up everything I\'ve learned about life: it goes on.',
    author: 'Robert Frost',
    category: 'life'
  },
  {
    id: '39',
    text: 'If you want to know what a man\'s like, take a good look at how he treats his inferiors, not his equals.',
    author: 'J.K. Rowling',
    category: 'wisdom'
  },
  {
    id: '40',
    text: 'Don\'t walk in front of me… I may not follow. Don\'t walk behind me… I may not lead. Walk beside me… just be my friend.',
    author: 'Albert Camus',
    category: 'philosophical'
  },
  {
    id: '41',
    text: 'If you tell the truth, you don\'t have to remember anything.',
    author: 'Mark Twain',
    category: 'wisdom'
  },
  {
    id: '42',
    text: 'Friendship is the only cement that will ever hold the world together.',
    author: 'Woodrow Wilson',
    category: 'wisdom'
  },
  {
    id: '43',
    text: 'A friend is someone who knows all about you and still loves you.',
    author: 'Elbert Hubbard',
    category: 'love'
  },
  {
    id: '44',
    text: 'To live is the rarest thing in the world. Most people just exist.',
    author: 'Oscar Wilde',
    category: 'life'
  },
  {
    id: '45',
    text: 'That which does not kill us makes us stronger.',
    author: 'Friedrich Nietzsche',
    category: 'philosophical'
  },
  {
    id: '46',
    text: 'Live as if you were to die tomorrow. Learn as if you were to live forever.',
    author: 'Mahatma Gandhi',
    category: 'wisdom'
  },
  {
    id: '47',
    text: 'We are all in the gutter, but some of us are looking at the stars.',
    author: 'Oscar Wilde',
    category: 'philosophical'
  },
  {
    id: '48',
    text: 'It is not a lack of love, but a lack of friendship that makes unhappy marriages.',
    author: 'Friedrich Nietzsche',
    category: 'love'
  },
  {
    id: '49',
    text: 'Good friends, good books, and a sleepy conscience: this is the ideal life.',
    author: 'Mark Twain',
    category: 'life'
  },
  {
    id: '50',
    text: 'Life is really simple, but we insist on making it complicated.',
    author: 'Confucius',
    category: 'wisdom'
  }
];

// Utility functions for quote management
export const getRandomQuote = (excludeId?: string): Quote => {
  const availableQuotes = excludeId
    ? quotes.filter(quote => quote.id !== excludeId)
    : quotes;

  const randomIndex = Math.floor(Math.random() * availableQuotes.length);
  return availableQuotes[randomIndex];
};

export const getQuotesByCategory = (category: QuoteCategory): Quote[] => {
  return quotes.filter(quote => quote.category === category);
};

export const searchQuotes = (searchTerm: string): Quote[] => {
  const term = searchTerm.toLowerCase();
  return quotes.filter(quote =>
    quote.text.toLowerCase().includes(term) ||
    quote.author.toLowerCase().includes(term) ||
    quote.category.toLowerCase().includes(term)
  );
};

export const getQuoteById = (id: string): Quote | undefined => {
  return quotes.find(quote => quote.id === id);
};
