export interface Quote {
  id: string;
  text: string;
  author: string;
  category: QuoteCategory;
  isFavorite?: boolean;
}

export type QuoteCategory =
  | 'motivational'
  | 'literary'
  | 'philosophical'
  | 'wisdom'
  | 'love'
  | 'success'
  | 'life'
  | 'music'
  | 'art'
  | 'science';

export const quotes: Quote[] = [
  {
    id: '1',
    text: 'Music is the universal language of mankind.',
    author: '<PERSON>',
    category: 'music'
  },
  {
    id: '2',
    text: 'Where words fail, music speaks.',
    author: '<PERSON>',
    category: 'music'
  },
  {
    id: '3',
    text: 'Music can heal the wounds which medicine cannot touch.',
    author: '<PERSON><PERSON><PERSON>',
    category: 'music'
  },
  {
    id: '4',
    text: 'The only way to do great work is to love what you do.',
    author: '<PERSON>',
    category: 'motivational'
  },
  {
    id: '5',
    text: 'Life is what happens to you while you\'re busy making other plans.',
    author: '<PERSON>',
    category: 'life'
  },
  {
    id: '6',
    text: 'The future belongs to those who believe in the beauty of their dreams.',
    author: '<PERSON>',
    category: 'motivational'
  },
  {
    id: '7',
    text: 'It is during our darkest moments that we must focus to see the light.',
    author: '<PERSON>',
    category: 'wisdom'
  },
  {
    id: '8',
    text: 'The way to get started is to quit talking and begin doing.',
    author: '<PERSON> <PERSON>',
    category: 'motivational'
  },
  {
    id: '9',
    text: 'Don\'t let yesterday take up too much of today.',
    author: 'Will Rogers',
    category: 'wisdom'
  },
  {
    id: '10',
    text: 'You learn more from failure than from success.',
    author: 'Unknown',
    category: 'wisdom'
  },
  {
    id: '11',
    text: 'If you are working on something that you really care about, you don\'t have to be pushed.',
    author: 'Steve Jobs',
    category: 'motivational'
  },
  {
    id: '12',
    text: 'Experience is the teacher of all things.',
    author: 'Julius Caesar',
    category: 'wisdom'
  },
  {
    id: '13',
    text: 'The best time to plant a tree was 20 years ago. The second best time is now.',
    author: 'Chinese Proverb',
    category: 'wisdom'
  },
  {
    id: '14',
    text: 'Your limitation—it\'s only your imagination.',
    author: 'Unknown',
    category: 'motivational'
  },
  {
    id: '15',
    text: 'Push yourself, because no one else is going to do it for you.',
    author: 'Unknown',
    category: 'motivational'
  },
  {
    id: '16',
    text: 'Great things never come from comfort zones.',
    author: 'Unknown',
    category: 'motivational'
  },
  {
    id: '17',
    text: 'Dream it. Wish it. Do it.',
    author: 'Unknown',
    category: 'motivational'
  },
  {
    id: '18',
    text: 'Success doesn\'t just find you. You have to go out and get it.',
    author: 'Unknown',
    category: 'success'
  },
  {
    id: '19',
    text: 'The harder you work for something, the greater you\'ll feel when you achieve it.',
    author: 'Unknown',
    category: 'success'
  },
  {
    id: '20',
    text: 'Dream bigger. Do bigger.',
    author: 'Unknown',
    category: 'motivational'
  },
  {
    id: '21',
    text: 'Don\'t stop when you\'re tired. Stop when you\'re done.',
    author: 'Unknown',
    category: 'motivational'
  },
  {
    id: '22',
    text: 'Wake up with determination. Go to bed with satisfaction.',
    author: 'Unknown',
    category: 'motivational'
  },
  {
    id: '23',
    text: 'Do something today that your future self will thank you for.',
    author: 'Sean Patrick Flanery',
    category: 'motivational'
  },
  {
    id: '24',
    text: 'Little things make big days.',
    author: 'Unknown',
    category: 'life'
  },
  {
    id: '25',
    text: 'It\'s going to be hard, but hard does not mean impossible.',
    author: 'Unknown',
    category: 'motivational'
  },
  {
    id: '26',
    text: 'Don\'t wait for opportunity. Create it.',
    author: 'Unknown',
    category: 'motivational'
  },
  {
    id: '27',
    text: 'Sometimes we\'re tested not to show our weaknesses, but to discover our strengths.',
    author: 'Unknown',
    category: 'wisdom'
  },
  {
    id: '28',
    text: 'The key to success is to focus on goals, not obstacles.',
    author: 'Unknown',
    category: 'success'
  },
  {
    id: '29',
    text: 'Dream it. Believe it. Build it.',
    author: 'Unknown',
    category: 'motivational'
  },
  {
    id: '30',
    text: 'What we think, we become.',
    author: 'Buddha',
    category: 'philosophical'
  },
  {
    id: '31',
    text: 'The mind is everything. What you think you become.',
    author: 'Buddha',
    category: 'philosophical'
  },
  {
    id: '32',
    text: 'Be yourself; everyone else is already taken.',
    author: 'Oscar Wilde',
    category: 'wisdom'
  },
  {
    id: '33',
    text: 'Two things are infinite: the universe and human stupidity; and I\'m not sure about the universe.',
    author: 'Albert Einstein',
    category: 'science'
  },
  {
    id: '34',
    text: 'So many books, so little time.',
    author: 'Frank Zappa',
    category: 'literary'
  },
  {
    id: '35',
    text: 'A room without books is like a body without a soul.',
    author: 'Marcus Tullius Cicero',
    category: 'literary'
  },
  {
    id: '36',
    text: 'You know you\'re in love when you can\'t fall asleep because reality is finally better than your dreams.',
    author: 'Dr. Seuss',
    category: 'love'
  },
  {
    id: '37',
    text: 'Be the change that you wish to see in the world.',
    author: 'Mahatma Gandhi',
    category: 'wisdom'
  },
  {
    id: '38',
    text: 'In three words I can sum up everything I\'ve learned about life: it goes on.',
    author: 'Robert Frost',
    category: 'life'
  },
  {
    id: '39',
    text: 'If you want to know what a man\'s like, take a good look at how he treats his inferiors, not his equals.',
    author: 'J.K. Rowling',
    category: 'wisdom'
  },
  {
    id: '40',
    text: 'Don\'t walk in front of me… I may not follow. Don\'t walk behind me… I may not lead. Walk beside me… just be my friend.',
    author: 'Albert Camus',
    category: 'philosophical'
  },
  {
    id: '41',
    text: 'If you tell the truth, you don\'t have to remember anything.',
    author: 'Mark Twain',
    category: 'wisdom'
  },
  {
    id: '42',
    text: 'Friendship is the only cement that will ever hold the world together.',
    author: 'Woodrow Wilson',
    category: 'wisdom'
  },
  {
    id: '43',
    text: 'A friend is someone who knows all about you and still loves you.',
    author: 'Elbert Hubbard',
    category: 'love'
  },
  {
    id: '44',
    text: 'To live is the rarest thing in the world. Most people just exist.',
    author: 'Oscar Wilde',
    category: 'life'
  },
  {
    id: '45',
    text: 'That which does not kill us makes us stronger.',
    author: 'Friedrich Nietzsche',
    category: 'philosophical'
  },
  {
    id: '46',
    text: 'Live as if you were to die tomorrow. Learn as if you were to live forever.',
    author: 'Mahatma Gandhi',
    category: 'wisdom'
  },
  {
    id: '47',
    text: 'We are all in the gutter, but some of us are looking at the stars.',
    author: 'Oscar Wilde',
    category: 'philosophical'
  },
  {
    id: '48',
    text: 'It is not a lack of love, but a lack of friendship that makes unhappy marriages.',
    author: 'Friedrich Nietzsche',
    category: 'love'
  },
  {
    id: '49',
    text: 'Good friends, good books, and a sleepy conscience: this is the ideal life.',
    author: 'Mark Twain',
    category: 'life'
  },
  {
    id: '50',
    text: 'Life is really simple, but we insist on making it complicated.',
    author: 'Confucius',
    category: 'wisdom'
  },
  {
    id: '51',
    text: 'The only impossible journey is the one you never begin.',
    author: 'Tony Robbins',
    category: 'motivational'
  },
  {
    id: '52',
    text: 'In the end, we will remember not the words of our enemies, but the silence of our friends.',
    author: 'Martin Luther King Jr.',
    category: 'wisdom'
  },
  {
    id: '53',
    text: 'The purpose of our lives is to be happy.',
    author: 'Dalai Lama',
    category: 'philosophical'
  },
  {
    id: '54',
    text: 'Life is what happens to you while you\'re busy making other plans.',
    author: 'Allen Saunders',
    category: 'life'
  },
  {
    id: '55',
    text: 'Get busy living or get busy dying.',
    author: 'Stephen King',
    category: 'motivational'
  },
  {
    id: '56',
    text: 'You have within you right now, everything you need to deal with whatever the world can throw at you.',
    author: 'Brian Tracy',
    category: 'motivational'
  },
  {
    id: '57',
    text: 'Believe you can and you\'re halfway there.',
    author: 'Theodore Roosevelt',
    category: 'motivational'
  },
  {
    id: '58',
    text: 'The only way to do great work is to love what you do.',
    author: 'Steve Jobs',
    category: 'success'
  },
  {
    id: '59',
    text: 'Life isn\'t about getting and having, it\'s about giving and being.',
    author: 'Kevin Kruse',
    category: 'life'
  },
  {
    id: '60',
    text: 'Whatever the mind of man can conceive and believe, it can achieve.',
    author: 'Napoleon Hill',
    category: 'motivational'
  },
  {
    id: '61',
    text: 'Strive not to be a success, but rather to be of value.',
    author: 'Albert Einstein',
    category: 'success'
  },
  {
    id: '62',
    text: 'Two roads diverged in a wood, and I—I took the one less traveled by, And that has made all the difference.',
    author: 'Robert Frost',
    category: 'life'
  },
  {
    id: '63',
    text: 'I attribute my success to this: I never gave or took any excuse.',
    author: 'Florence Nightingale',
    category: 'success'
  },
  {
    id: '64',
    text: 'You miss 100% of the shots you don\'t take.',
    author: 'Wayne Gretzky',
    category: 'motivational'
  },
  {
    id: '65',
    text: 'I\'ve missed more than 9000 shots in my career. I\'ve lost almost 300 games. 26 times, I\'ve been trusted to take the game winning shot and missed. I\'ve failed over and over and over again in my life. And that is why I succeed.',
    author: 'Michael Jordan',
    category: 'success'
  },
  {
    id: '66',
    text: 'The most difficult thing is the decision to act, the rest is merely tenacity.',
    author: 'Amelia Earhart',
    category: 'motivational'
  },
  {
    id: '67',
    text: 'Every strike brings me closer to the next home run.',
    author: 'Babe Ruth',
    category: 'motivational'
  },
  {
    id: '68',
    text: 'Definiteness of purpose is the starting point of all achievement.',
    author: 'W. Clement Stone',
    category: 'success'
  },
  {
    id: '69',
    text: 'Life is 10% what happens to me and 90% of how I react to it.',
    author: 'Charles Swindoll',
    category: 'wisdom'
  },
  {
    id: '70',
    text: 'The most common way people give up their power is by thinking they don\'t have any.',
    author: 'Alice Walker',
    category: 'motivational'
  }
];

// Utility functions for quote management
export const getRandomQuote = (excludeId?: string): Quote => {
  const availableQuotes = excludeId
    ? quotes.filter(quote => quote.id !== excludeId)
    : quotes;

  const randomIndex = Math.floor(Math.random() * availableQuotes.length);
  return availableQuotes[randomIndex];
};

export const getQuotesByCategory = (category: QuoteCategory): Quote[] => {
  return quotes.filter(quote => quote.category === category);
};

export const searchQuotes = (searchTerm: string): Quote[] => {
  const term = searchTerm.toLowerCase();
  return quotes.filter(quote =>
    quote.text.toLowerCase().includes(term) ||
    quote.author.toLowerCase().includes(term) ||
    quote.category.toLowerCase().includes(term)
  );
};

export const getQuoteById = (id: string): Quote | undefined => {
  return quotes.find(quote => quote.id === id);
};
