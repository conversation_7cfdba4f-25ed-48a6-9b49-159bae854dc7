/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/../data/quotes`; params?: Router.UnknownInputParams; } | { pathname: `/../hooks/useQuotes`; params?: Router.UnknownInputParams; } | { pathname: `/../hooks/useMusicPlayer`; params?: Router.UnknownInputParams; } | { pathname: `/../components/quotes/QuoteDisplay`; params?: Router.UnknownInputParams; } | { pathname: `/../components/quotes/QuoteControls`; params?: Router.UnknownInputParams; } | { pathname: `/../components/quotes/QuotesWidget`; params?: Router.UnknownInputParams; } | { pathname: `/../components/music/PlayerControls`; params?: Router.UnknownInputParams; } | { pathname: `/../components/music/ProgressBar`; params?: Router.UnknownInputParams; } | { pathname: `/../components/music/TrackInfo`; params?: Router.UnknownInputParams; } | { pathname: `/../components/music/MusicPlayer`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/explore` | `/explore`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/../data/quotes`; params?: Router.UnknownOutputParams; } | { pathname: `/../hooks/useQuotes`; params?: Router.UnknownOutputParams; } | { pathname: `/../hooks/useMusicPlayer`; params?: Router.UnknownOutputParams; } | { pathname: `/../components/quotes/QuoteDisplay`; params?: Router.UnknownOutputParams; } | { pathname: `/../components/quotes/QuoteControls`; params?: Router.UnknownOutputParams; } | { pathname: `/../components/quotes/QuotesWidget`; params?: Router.UnknownOutputParams; } | { pathname: `/../components/music/PlayerControls`; params?: Router.UnknownOutputParams; } | { pathname: `/../components/music/ProgressBar`; params?: Router.UnknownOutputParams; } | { pathname: `/../components/music/TrackInfo`; params?: Router.UnknownOutputParams; } | { pathname: `/../components/music/MusicPlayer`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/explore` | `/explore`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `/+not-found`, params: Router.UnknownOutputParams & {  } };
      href: Router.RelativePathString | Router.ExternalPathString | `/../data/quotes${`?${string}` | `#${string}` | ''}` | `/../hooks/useQuotes${`?${string}` | `#${string}` | ''}` | `/../hooks/useMusicPlayer${`?${string}` | `#${string}` | ''}` | `/../components/quotes/QuoteDisplay${`?${string}` | `#${string}` | ''}` | `/../components/quotes/QuoteControls${`?${string}` | `#${string}` | ''}` | `/../components/quotes/QuotesWidget${`?${string}` | `#${string}` | ''}` | `/../components/music/PlayerControls${`?${string}` | `#${string}` | ''}` | `/../components/music/ProgressBar${`?${string}` | `#${string}` | ''}` | `/../components/music/TrackInfo${`?${string}` | `#${string}` | ''}` | `/../components/music/MusicPlayer${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/explore${`?${string}` | `#${string}` | ''}` | `/explore${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/../data/quotes`; params?: Router.UnknownInputParams; } | { pathname: `/../hooks/useQuotes`; params?: Router.UnknownInputParams; } | { pathname: `/../hooks/useMusicPlayer`; params?: Router.UnknownInputParams; } | { pathname: `/../components/quotes/QuoteDisplay`; params?: Router.UnknownInputParams; } | { pathname: `/../components/quotes/QuoteControls`; params?: Router.UnknownInputParams; } | { pathname: `/../components/quotes/QuotesWidget`; params?: Router.UnknownInputParams; } | { pathname: `/../components/music/PlayerControls`; params?: Router.UnknownInputParams; } | { pathname: `/../components/music/ProgressBar`; params?: Router.UnknownInputParams; } | { pathname: `/../components/music/TrackInfo`; params?: Router.UnknownInputParams; } | { pathname: `/../components/music/MusicPlayer`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/explore` | `/explore`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | `/+not-found` | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } };
    }
  }
}
