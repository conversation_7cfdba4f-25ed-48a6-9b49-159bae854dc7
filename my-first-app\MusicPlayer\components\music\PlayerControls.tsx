import React from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';
import Animated, { useSharedValue, useAnimatedStyle, withSpring } from 'react-native-reanimated';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

interface PlayerControlsProps {
  isPlaying: boolean;
  isShuffled: boolean;
  repeatMode: 'none' | 'one' | 'all';
  onPlayPause: () => void;
  onPrevious: () => void;
  onNext: () => void;
  onShuffle: () => void;
  onRepeat: () => void;
}

export function PlayerControls({
  isPlaying,
  isShuffled,
  repeatMode,
  onPlayPause,
  onPrevious,
  onNext,
  onShuffle,
  onRepeat,
}: PlayerControlsProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const playPauseScale = useSharedValue(1);
  const prevScale = useSharedValue(1);
  const nextScale = useSharedValue(1);
  const shuffleScale = useSharedValue(1);
  const repeatScale = useSharedValue(1);

  const playPauseAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: playPauseScale.value }],
  }));

  const prevAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: prevScale.value }],
  }));

  const nextAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: nextScale.value }],
  }));

  const shuffleAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: shuffleScale.value }],
  }));

  const repeatAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: repeatScale.value }],
  }));

  const handlePlayPausePress = () => {
    playPauseScale.value = withSpring(0.9, { duration: 100 }, () => {
      playPauseScale.value = withSpring(1, { duration: 100 });
    });
    onPlayPause();
  };

  const handlePreviousPress = () => {
    prevScale.value = withSpring(0.9, { duration: 100 }, () => {
      prevScale.value = withSpring(1, { duration: 100 });
    });
    onPrevious();
  };

  const handleNextPress = () => {
    nextScale.value = withSpring(0.9, { duration: 100 }, () => {
      nextScale.value = withSpring(1, { duration: 100 });
    });
    onNext();
  };

  const handleShufflePress = () => {
    shuffleScale.value = withSpring(0.9, { duration: 100 }, () => {
      shuffleScale.value = withSpring(1, { duration: 100 });
    });
    onShuffle();
  };

  const handleRepeatPress = () => {
    repeatScale.value = withSpring(0.9, { duration: 100 }, () => {
      repeatScale.value = withSpring(1, { duration: 100 });
    });
    onRepeat();
  };

  const getRepeatIcon = () => {
    switch (repeatMode) {
      case 'one':
        return 'repeat.1';
      case 'all':
        return 'repeat';
      default:
        return 'repeat';
    }
  };

  const getRepeatColor = () => {
    return repeatMode !== 'none' ? Colors[colorScheme].tint : Colors[colorScheme].icon;
  };

  return (
    <View style={styles.container}>
      {/* Secondary Controls */}
      <View style={styles.secondaryControls}>
        <Animated.View style={shuffleAnimatedStyle}>
          <TouchableOpacity
            onPress={handleShufflePress}
            style={[
              styles.secondaryButton,
              isShuffled && { backgroundColor: Colors[colorScheme].tint + '20' }
            ]}
            activeOpacity={0.7}
          >
            <IconSymbol
              name="shuffle"
              size={20}
              color={isShuffled ? Colors[colorScheme].tint : Colors[colorScheme].icon}
            />
          </TouchableOpacity>
        </Animated.View>

        <Animated.View style={repeatAnimatedStyle}>
          <TouchableOpacity
            onPress={handleRepeatPress}
            style={[
              styles.secondaryButton,
              repeatMode !== 'none' && { backgroundColor: Colors[colorScheme].tint + '20' }
            ]}
            activeOpacity={0.7}
          >
            <IconSymbol
              name={getRepeatIcon()}
              size={20}
              color={getRepeatColor()}
            />
          </TouchableOpacity>
        </Animated.View>
      </View>

      {/* Main Controls */}
      <View style={styles.mainControls}>
        <Animated.View style={prevAnimatedStyle}>
          <TouchableOpacity
            onPress={handlePreviousPress}
            style={styles.controlButton}
            activeOpacity={0.7}
          >
            <IconSymbol
              name="backward.fill"
              size={32}
              color={Colors[colorScheme].text}
            />
          </TouchableOpacity>
        </Animated.View>

        <Animated.View style={playPauseAnimatedStyle}>
          <TouchableOpacity
            onPress={handlePlayPausePress}
            style={[styles.playPauseButton, { backgroundColor: Colors[colorScheme].tint }]}
            activeOpacity={0.8}
          >
            <IconSymbol
              name={isPlaying ? "pause.fill" : "play.fill"}
              size={36}
              color="#FFFFFF"
            />
          </TouchableOpacity>
        </Animated.View>

        <Animated.View style={nextAnimatedStyle}>
          <TouchableOpacity
            onPress={handleNextPress}
            style={styles.controlButton}
            activeOpacity={0.7}
          >
            <IconSymbol
              name="forward.fill"
              size={32}
              color={Colors[colorScheme].text}
            />
          </TouchableOpacity>
        </Animated.View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    gap: 20,
  },
  secondaryControls: {
    flexDirection: 'row',
    gap: 40,
    justifyContent: 'center',
  },
  secondaryButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  mainControls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 24,
  },
  controlButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  playPauseButton: {
    width: 72,
    height: 72,
    borderRadius: 36,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
});
