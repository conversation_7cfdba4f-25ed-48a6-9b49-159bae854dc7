import React from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';
import Animated, { 
  FadeInUp, 
  FadeOutDown,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  interpolateColor
} from 'react-native-reanimated';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Quote } from '@/data/quotes';

interface QuoteDisplayProps {
  quote: Quote;
  isFavorite: boolean;
  onToggleFavorite: () => void;
  onShare?: () => void;
}

export function QuoteDisplay({ quote, isFavorite, onToggleFavorite, onShare }: QuoteDisplayProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const favoriteScale = useSharedValue(1);
  
  const favoriteAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: favoriteScale.value }],
  }));

  const handleFavoritePress = () => {
    favoriteScale.value = withSpring(0.8, { duration: 100 }, () => {
      favoriteScale.value = withSpring(1, { duration: 100 });
    });
    onToggleFavorite();
  };

  const getCategoryColor = (category: string) => {
    const categoryColors = {
      motivational: '#FF6B6B',
      literary: '#4ECDC4',
      philosophical: '#45B7D1',
      wisdom: '#96CEB4',
      love: '#FFEAA7',
      success: '#DDA0DD',
      life: '#74B9FF',
      music: '#FD79A8',
      art: '#FDCB6E',
      science: '#6C5CE7',
    };
    return categoryColors[category as keyof typeof categoryColors] || Colors[colorScheme].tint;
  };

  return (
    <Animated.View
      entering={FadeInUp.duration(500)}
      exiting={FadeOutDown.duration(300)}
      style={styles.container}
    >
      <ThemedView style={[styles.quoteCard, { borderLeftColor: getCategoryColor(quote.category) }]}>
        {/* Category Badge */}
        <View style={[styles.categoryBadge, { backgroundColor: getCategoryColor(quote.category) }]}>
          <ThemedText style={styles.categoryText}>{quote.category.toUpperCase()}</ThemedText>
        </View>

        {/* Quote Text */}
        <ThemedText style={styles.quoteText}>"{quote.text}"</ThemedText>
        
        {/* Author */}
        <ThemedText style={styles.authorText}>— {quote.author}</ThemedText>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <Animated.View style={favoriteAnimatedStyle}>
            <TouchableOpacity
              onPress={handleFavoritePress}
              style={[
                styles.actionButton,
                isFavorite && { backgroundColor: Colors[colorScheme].tint + '20' }
              ]}
              activeOpacity={0.7}
            >
              <IconSymbol
                name={isFavorite ? "heart.fill" : "heart"}
                size={24}
                color={isFavorite ? '#FF6B6B' : Colors[colorScheme].icon}
              />
            </TouchableOpacity>
          </Animated.View>

          {onShare && (
            <TouchableOpacity
              onPress={onShare}
              style={styles.actionButton}
              activeOpacity={0.7}
            >
              <IconSymbol
                name="square.and.arrow.up"
                size={24}
                color={Colors[colorScheme].icon}
              />
            </TouchableOpacity>
          )}
        </View>
      </ThemedView>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  quoteCard: {
    width: '100%',
    maxWidth: 400,
    padding: 24,
    borderRadius: 16,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  categoryBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 16,
  },
  categoryText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#FFFFFF',
    letterSpacing: 1,
  },
  quoteText: {
    fontSize: 18,
    lineHeight: 28,
    fontStyle: 'italic',
    textAlign: 'center',
    marginBottom: 16,
    fontWeight: '400',
  },
  authorText: {
    fontSize: 14,
    textAlign: 'right',
    opacity: 0.8,
    marginBottom: 20,
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 16,
  },
  actionButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
});
