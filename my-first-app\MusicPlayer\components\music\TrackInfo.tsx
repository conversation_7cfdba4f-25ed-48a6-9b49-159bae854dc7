import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Image } from 'expo-image';
import Animated, { FadeInUp, FadeOutDown } from 'react-native-reanimated';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Track } from '@/hooks/useMusicPlayer';

interface TrackInfoProps {
  track: Track;
}

export function TrackInfo({ track }: TrackInfoProps) {
  return (
    <Animated.View
      entering={FadeInUp.duration(500)}
      exiting={FadeOutDown.duration(300)}
      style={styles.container}
    >
      <ThemedView style={styles.albumArtContainer}>
        <Image
          source={{ uri: track.albumArt || 'https://via.placeholder.com/300x300/CCCCCC/FFFFFF?text=No+Image' }}
          style={styles.albumArt}
          contentFit="cover"
          transition={300}
        />
        
        {/* Vinyl Record Effect */}
        <View style={styles.vinylOverlay}>
          <View style={styles.vinylCenter} />
        </View>
      </ThemedView>

      <View style={styles.trackDetails}>
        <ThemedText style={styles.trackTitle} numberOfLines={2}>
          {track.title}
        </ThemedText>
        <ThemedText style={styles.artistName} numberOfLines={1}>
          {track.artist}
        </ThemedText>
      </View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    paddingHorizontal: 20,
    gap: 24,
  },
  albumArtContainer: {
    width: 280,
    height: 280,
    borderRadius: 140,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 16,
    position: 'relative',
    overflow: 'hidden',
  },
  albumArt: {
    width: '100%',
    height: '100%',
    borderRadius: 140,
  },
  vinylOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.1)',
    borderRadius: 140,
  },
  vinylCenter: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(0,0,0,0.3)',
    borderWidth: 2,
    borderColor: 'rgba(255,255,255,0.2)',
  },
  trackDetails: {
    alignItems: 'center',
    gap: 8,
    maxWidth: '100%',
  },
  trackTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    lineHeight: 30,
  },
  artistName: {
    fontSize: 18,
    opacity: 0.7,
    textAlign: 'center',
    fontWeight: '500',
  },
});
