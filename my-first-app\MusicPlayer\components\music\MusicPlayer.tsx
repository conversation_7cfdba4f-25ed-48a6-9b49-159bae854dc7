import React from 'react';
import { StyleSheet, View } from 'react-native';
import { ThemedView } from '@/components/ThemedView';
import { TrackInfo } from './TrackInfo';
import { ProgressBar } from './ProgressBar';
import { PlayerControls } from './PlayerControls';
import { useMusicPlayer } from '@/hooks/useMusicPlayer';

export function MusicPlayer() {
  const {
    currentTrack,
    isPlaying,
    currentTime,
    isShuffled,
    repeatMode,
    isLoading,
    togglePlayPause,
    nextTrack,
    previousTrack,
    seekTo,
    toggleShuffle,
    toggleRepeat,
    formatTime,
  } = useMusicPlayer();

  if (isLoading || !currentTrack) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.loadingContainer}>
          {/* You could add a loading spinner here */}
        </View>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <View style={styles.playerContent}>
        {/* Track Information */}
        <View style={styles.trackInfoContainer}>
          <TrackInfo track={currentTrack} />
        </View>

        {/* Progress Bar */}
        <View style={styles.progressContainer}>
          <ProgressBar
            currentTime={currentTime}
            duration={currentTrack.duration}
            onSeek={seekTo}
            formatTime={formatTime}
          />
        </View>

        {/* Player Controls */}
        <View style={styles.controlsContainer}>
          <PlayerControls
            isPlaying={isPlaying}
            isShuffled={isShuffled}
            repeatMode={repeatMode}
            onPlayPause={togglePlayPause}
            onPrevious={previousTrack}
            onNext={nextTrack}
            onShuffle={toggleShuffle}
            onRepeat={toggleRepeat}
          />
        </View>
      </View>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  playerContent: {
    flex: 1,
    justifyContent: 'space-between',
    paddingVertical: 40,
  },
  trackInfoContainer: {
    flex: 1,
    justifyContent: 'center',
    minHeight: 350,
  },
  progressContainer: {
    paddingVertical: 20,
  },
  controlsContainer: {
    paddingBottom: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
