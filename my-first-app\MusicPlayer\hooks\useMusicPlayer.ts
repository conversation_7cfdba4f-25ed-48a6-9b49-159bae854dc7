import { useState, useEffect, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface Track {
  id: string;
  title: string;
  artist: string;
  duration: number; // in seconds
  uri?: string; // For actual audio files
  albumArt?: string;
}

export interface MusicPlayerState {
  currentTrack: Track | null;
  playlist: Track[];
  isPlaying: boolean;
  currentTime: number;
  volume: number;
  isShuffled: boolean;
  repeatMode: 'none' | 'one' | 'all';
  isLoading: boolean;
}

const PLAYER_STATE_STORAGE_KEY = '@music_player_state';
const PLAYLIST_STORAGE_KEY = '@music_player_playlist';

// Sample tracks for demo purposes
const sampleTracks: Track[] = [
  {
    id: '1',
    title: 'Bohemian Rhapsody',
    artist: 'Queen',
    duration: 355,
    albumArt: 'https://via.placeholder.com/300x300/FF6B6B/FFFFFF?text=Queen'
  },
  {
    id: '2',
    title: 'Imagine',
    artist: '<PERSON>',
    duration: 183,
    albumArt: 'https://via.placeholder.com/300x300/4ECDC4/FFFFFF?text=Lennon'
  },
  {
    id: '3',
    title: 'Hotel California',
    artist: 'Eagles',
    duration: 391,
    albumArt: 'https://via.placeholder.com/300x300/45B7D1/FFFFFF?text=Eagles'
  },
  {
    id: '4',
    title: 'Stairway to Heaven',
    artist: 'Led Zeppelin',
    duration: 482,
    albumArt: 'https://via.placeholder.com/300x300/96CEB4/FFFFFF?text=Zeppelin'
  },
  {
    id: '5',
    title: 'Sweet Child O\' Mine',
    artist: 'Guns N\' Roses',
    duration: 356,
    albumArt: 'https://via.placeholder.com/300x300/FFEAA7/000000?text=GNR'
  },
  {
    id: '6',
    title: 'Billie Jean',
    artist: 'Michael Jackson',
    duration: 294,
    albumArt: 'https://via.placeholder.com/300x300/DDA0DD/FFFFFF?text=MJ'
  },
  {
    id: '7',
    title: 'Smells Like Teen Spirit',
    artist: 'Nirvana',
    duration: 301,
    albumArt: 'https://via.placeholder.com/300x300/74B9FF/FFFFFF?text=Nirvana'
  },
  {
    id: '8',
    title: 'Yesterday',
    artist: 'The Beatles',
    duration: 125,
    albumArt: 'https://via.placeholder.com/300x300/FD79A8/FFFFFF?text=Beatles'
  }
];

export const useMusicPlayer = () => {
  const [state, setState] = useState<MusicPlayerState>({
    currentTrack: null,
    playlist: sampleTracks,
    isPlaying: false,
    currentTime: 0,
    volume: 0.7,
    isShuffled: false,
    repeatMode: 'none',
    isLoading: true,
  });

  // Load saved state on mount
  useEffect(() => {
    loadSavedState();
  }, []);

  // Simulate playback progress
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (state.isPlaying && state.currentTrack) {
      interval = setInterval(() => {
        setState(prev => {
          const newTime = prev.currentTime + 1;
          
          // Auto-advance to next track when current track ends
          if (newTime >= (prev.currentTrack?.duration || 0)) {
            if (prev.repeatMode === 'one') {
              return { ...prev, currentTime: 0 };
            } else {
              // Auto-advance to next track
              const nextTrack = getNextTrack(prev.currentTrack!, prev.playlist, prev.isShuffled);
              if (nextTrack || prev.repeatMode === 'all') {
                return {
                  ...prev,
                  currentTrack: nextTrack || prev.playlist[0],
                  currentTime: 0,
                };
              } else {
                return { ...prev, isPlaying: false, currentTime: 0 };
              }
            }
          }
          
          return { ...prev, currentTime: newTime };
        });
      }, 1000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [state.isPlaying, state.currentTrack, state.repeatMode]);

  const loadSavedState = async () => {
    try {
      const [playerStateData, playlistData] = await Promise.all([
        AsyncStorage.getItem(PLAYER_STATE_STORAGE_KEY),
        AsyncStorage.getItem(PLAYLIST_STORAGE_KEY),
      ]);

      const savedState = playerStateData ? JSON.parse(playerStateData) : {};
      const savedPlaylist = playlistData ? JSON.parse(playlistData) : sampleTracks;

      setState(prev => ({
        ...prev,
        ...savedState,
        playlist: savedPlaylist,
        currentTrack: savedState.currentTrack || savedPlaylist[0],
        isPlaying: false, // Always start paused
        isLoading: false,
      }));
    } catch (error) {
      console.error('Error loading saved player state:', error);
      setState(prev => ({
        ...prev,
        currentTrack: sampleTracks[0],
        isLoading: false,
      }));
    }
  };

  const savePlayerState = async (newState: Partial<MusicPlayerState>) => {
    try {
      const stateToSave = {
        currentTrack: newState.currentTrack,
        volume: newState.volume,
        isShuffled: newState.isShuffled,
        repeatMode: newState.repeatMode,
      };
      await AsyncStorage.setItem(PLAYER_STATE_STORAGE_KEY, JSON.stringify(stateToSave));
    } catch (error) {
      console.error('Error saving player state:', error);
    }
  };

  const getNextTrack = (currentTrack: Track, playlist: Track[], isShuffled: boolean): Track | null => {
    if (isShuffled) {
      const availableTracks = playlist.filter(track => track.id !== currentTrack.id);
      if (availableTracks.length === 0) return null;
      return availableTracks[Math.floor(Math.random() * availableTracks.length)];
    }
    
    const currentIndex = playlist.findIndex(track => track.id === currentTrack.id);
    const nextIndex = currentIndex + 1;
    return nextIndex < playlist.length ? playlist[nextIndex] : null;
  };

  const getPreviousTrack = (currentTrack: Track, playlist: Track[], isShuffled: boolean): Track | null => {
    if (isShuffled) {
      const availableTracks = playlist.filter(track => track.id !== currentTrack.id);
      if (availableTracks.length === 0) return null;
      return availableTracks[Math.floor(Math.random() * availableTracks.length)];
    }
    
    const currentIndex = playlist.findIndex(track => track.id === currentTrack.id);
    const prevIndex = currentIndex - 1;
    return prevIndex >= 0 ? playlist[prevIndex] : null;
  };

  const play = useCallback(() => {
    setState(prev => ({ ...prev, isPlaying: true }));
  }, []);

  const pause = useCallback(() => {
    setState(prev => ({ ...prev, isPlaying: false }));
  }, []);

  const togglePlayPause = useCallback(() => {
    setState(prev => ({ ...prev, isPlaying: !prev.isPlaying }));
  }, []);

  const nextTrack = useCallback(() => {
    if (!state.currentTrack) return;
    
    const next = getNextTrack(state.currentTrack, state.playlist, state.isShuffled);
    if (next) {
      const newState = { currentTrack: next, currentTime: 0 };
      setState(prev => ({ ...prev, ...newState }));
      savePlayerState({ ...state, ...newState });
    }
  }, [state.currentTrack, state.playlist, state.isShuffled]);

  const previousTrack = useCallback(() => {
    if (!state.currentTrack) return;
    
    // If more than 3 seconds have passed, restart current track
    if (state.currentTime > 3) {
      setState(prev => ({ ...prev, currentTime: 0 }));
      return;
    }
    
    const prev = getPreviousTrack(state.currentTrack, state.playlist, state.isShuffled);
    if (prev) {
      const newState = { currentTrack: prev, currentTime: 0 };
      setState(prevState => ({ ...prevState, ...newState }));
      savePlayerState({ ...state, ...newState });
    }
  }, [state.currentTrack, state.playlist, state.isShuffled, state.currentTime]);

  const seekTo = useCallback((time: number) => {
    setState(prev => ({ ...prev, currentTime: Math.max(0, Math.min(time, prev.currentTrack?.duration || 0)) }));
  }, []);

  const setVolume = useCallback(async (volume: number) => {
    const newVolume = Math.max(0, Math.min(1, volume));
    setState(prev => ({ ...prev, volume: newVolume }));
    await savePlayerState({ ...state, volume: newVolume });
  }, [state]);

  const toggleShuffle = useCallback(async () => {
    const newShuffled = !state.isShuffled;
    setState(prev => ({ ...prev, isShuffled: newShuffled }));
    await savePlayerState({ ...state, isShuffled: newShuffled });
  }, [state]);

  const toggleRepeat = useCallback(async () => {
    const modes: Array<'none' | 'one' | 'all'> = ['none', 'one', 'all'];
    const currentIndex = modes.indexOf(state.repeatMode);
    const nextMode = modes[(currentIndex + 1) % modes.length];
    setState(prev => ({ ...prev, repeatMode: nextMode }));
    await savePlayerState({ ...state, repeatMode: nextMode });
  }, [state]);

  const selectTrack = useCallback(async (track: Track) => {
    const newState = { currentTrack: track, currentTime: 0 };
    setState(prev => ({ ...prev, ...newState }));
    await savePlayerState({ ...state, ...newState });
  }, [state]);

  const formatTime = useCallback((seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }, []);

  return {
    ...state,
    play,
    pause,
    togglePlayPause,
    nextTrack,
    previousTrack,
    seekTo,
    setVolume,
    toggleShuffle,
    toggleRepeat,
    selectTrack,
    formatTime,
  };
};
