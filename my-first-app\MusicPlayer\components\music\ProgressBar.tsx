import React, { useState } from 'react';
import { StyleSheet, View, PanGestureHandler, PanGestureHandlerGestureEvent } from 'react-native';
import Animated, {
  useAnimatedGestureHandler,
  useAnimatedStyle,
  useSharedValue,
  runOnJS,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';
import { ThemedText } from '@/components/ThemedText';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

interface ProgressBarProps {
  currentTime: number;
  duration: number;
  onSeek: (time: number) => void;
  formatTime: (seconds: number) => string;
}

export function ProgressBar({ currentTime, duration, onSeek, formatTime }: ProgressBarProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const [isDragging, setIsDragging] = useState(false);
  const [dragTime, setDragTime] = useState(0);
  
  const translateX = useSharedValue(0);
  const progressWidth = useSharedValue(0);

  const progress = duration > 0 ? currentTime / duration : 0;
  const displayTime = isDragging ? dragTime : currentTime;

  const gestureHandler = useAnimatedGestureHandler<PanGestureHandlerGestureEvent>({
    onStart: () => {
      runOnJS(setIsDragging)(true);
    },
    onActive: (event) => {
      const clampedX = Math.max(0, Math.min(event.x, progressWidth.value));
      translateX.value = clampedX;
      
      const newProgress = clampedX / progressWidth.value;
      const newTime = newProgress * duration;
      runOnJS(setDragTime)(newTime);
    },
    onEnd: (event) => {
      const clampedX = Math.max(0, Math.min(event.x, progressWidth.value));
      const newProgress = clampedX / progressWidth.value;
      const newTime = newProgress * duration;
      
      runOnJS(onSeek)(newTime);
      runOnJS(setIsDragging)(false);
      runOnJS(setDragTime)(0);
    },
  });

  const thumbAnimatedStyle = useAnimatedStyle(() => {
    const thumbPosition = isDragging 
      ? translateX.value 
      : interpolate(
          progress,
          [0, 1],
          [0, progressWidth.value],
          Extrapolate.CLAMP
        );

    return {
      transform: [{ translateX: thumbPosition - 12 }], // 12 is half of thumb width
    };
  });

  const progressAnimatedStyle = useAnimatedStyle(() => {
    const width = isDragging 
      ? translateX.value 
      : interpolate(
          progress,
          [0, 1],
          [0, progressWidth.value],
          Extrapolate.CLAMP
        );

    return {
      width: Math.max(0, width),
    };
  });

  return (
    <View style={styles.container}>
      {/* Time Display */}
      <View style={styles.timeContainer}>
        <ThemedText style={styles.timeText}>
          {formatTime(displayTime)}
        </ThemedText>
        <ThemedText style={styles.timeText}>
          {formatTime(duration)}
        </ThemedText>
      </View>

      {/* Progress Bar */}
      <PanGestureHandler onGestureEvent={gestureHandler}>
        <Animated.View
          style={styles.progressContainer}
          onLayout={(event) => {
            progressWidth.value = event.nativeEvent.layout.width;
          }}
        >
          {/* Background Track */}
          <View style={[styles.progressTrack, { backgroundColor: Colors[colorScheme].icon + '30' }]} />
          
          {/* Progress Fill */}
          <Animated.View
            style={[
              styles.progressFill,
              { backgroundColor: Colors[colorScheme].tint },
              progressAnimatedStyle,
            ]}
          />
          
          {/* Thumb */}
          <Animated.View
            style={[
              styles.progressThumb,
              { backgroundColor: Colors[colorScheme].tint },
              thumbAnimatedStyle,
            ]}
          />
        </Animated.View>
      </PanGestureHandler>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    gap: 8,
  },
  timeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  timeText: {
    fontSize: 12,
    opacity: 0.7,
    fontWeight: '500',
  },
  progressContainer: {
    height: 40,
    justifyContent: 'center',
    position: 'relative',
  },
  progressTrack: {
    height: 4,
    borderRadius: 2,
    position: 'absolute',
    left: 0,
    right: 0,
  },
  progressFill: {
    height: 4,
    borderRadius: 2,
    position: 'absolute',
    left: 0,
  },
  progressThumb: {
    width: 24,
    height: 24,
    borderRadius: 12,
    position: 'absolute',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 4,
  },
});
