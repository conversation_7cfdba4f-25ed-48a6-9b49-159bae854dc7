import React from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';
import Animated, {
  useAnimatedStyle,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';
import { ThemedText } from '@/components/ThemedText';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

interface ProgressBarProps {
  currentTime: number;
  duration: number;
  onSeek: (time: number) => void;
  formatTime: (seconds: number) => string;
}

export function ProgressBar({ currentTime, duration, onSeek, formatTime }: ProgressBarProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const progress = duration > 0 ? currentTime / duration : 0;

  const progressAnimatedStyle = useAnimatedStyle(() => {
    return {
      width: `${Math.max(0, Math.min(100, progress * 100))}%`,
    };
  });

  const handleProgressPress = (event: any) => {
    const { locationX, target } = event.nativeEvent;
    target.measure((x: number, y: number, width: number) => {
      const newProgress = locationX / width;
      const newTime = newProgress * duration;
      onSeek(newTime);
    });
  };

  return (
    <View style={styles.container}>
      {/* Time Display */}
      <View style={styles.timeContainer}>
        <ThemedText style={styles.timeText}>
          {formatTime(currentTime)}
        </ThemedText>
        <ThemedText style={styles.timeText}>
          {formatTime(duration)}
        </ThemedText>
      </View>

      {/* Progress Bar */}
      <TouchableOpacity
        style={styles.progressContainer}
        onPress={handleProgressPress}
        activeOpacity={0.8}
      >
        {/* Background Track */}
        <View style={[styles.progressTrack, { backgroundColor: Colors[colorScheme].icon + '30' }]} />

        {/* Progress Fill */}
        <Animated.View
          style={[
            styles.progressFill,
            { backgroundColor: Colors[colorScheme].tint },
            progressAnimatedStyle,
          ]}
        />
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    gap: 8,
  },
  timeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  timeText: {
    fontSize: 12,
    opacity: 0.7,
    fontWeight: '500',
  },
  progressContainer: {
    height: 40,
    justifyContent: 'center',
    position: 'relative',
  },
  progressTrack: {
    height: 4,
    borderRadius: 2,
    position: 'absolute',
    left: 0,
    right: 0,
  },
  progressFill: {
    height: 4,
    borderRadius: 2,
    position: 'absolute',
    left: 0,
  },
});
