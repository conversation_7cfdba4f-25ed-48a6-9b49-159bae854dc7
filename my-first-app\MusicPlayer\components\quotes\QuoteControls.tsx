import React from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';
import Animated, { useSharedValue, useAnimatedStyle, withSpring } from 'react-native-reanimated';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

interface QuoteControlsProps {
  isAutoRotating: boolean;
  autoRotateInterval: number;
  onToggleAutoRotate: () => void;
  onPrevious: () => void;
  onNext: () => void;
  onIntervalChange: (interval: number) => void;
}

const INTERVAL_OPTIONS = [10, 15, 30, 60, 120]; // seconds

export function QuoteControls({
  isAutoRotating,
  autoRotateInterval,
  onToggleAutoRotate,
  onPrevious,
  onNext,
  onIntervalChange,
}: QuoteControlsProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const autoRotateScale = useSharedValue(1);
  const prevScale = useSharedValue(1);
  const nextScale = useSharedValue(1);

  const autoRotateAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: autoRotateScale.value }],
  }));

  const prevAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: prevScale.value }],
  }));

  const nextAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: nextScale.value }],
  }));

  const handleAutoRotatePress = () => {
    autoRotateScale.value = withSpring(0.9, { duration: 100 }, () => {
      autoRotateScale.value = withSpring(1, { duration: 100 });
    });
    onToggleAutoRotate();
  };

  const handlePreviousPress = () => {
    prevScale.value = withSpring(0.9, { duration: 100 }, () => {
      prevScale.value = withSpring(1, { duration: 100 });
    });
    onPrevious();
  };

  const handleNextPress = () => {
    nextScale.value = withSpring(0.9, { duration: 100 }, () => {
      nextScale.value = withSpring(1, { duration: 100 });
    });
    onNext();
  };

  const formatInterval = (seconds: number): string => {
    if (seconds < 60) {
      return `${seconds}s`;
    }
    return `${Math.floor(seconds / 60)}m`;
  };

  return (
    <ThemedView style={styles.container}>
      {/* Navigation Controls */}
      <View style={styles.navigationControls}>
        <Animated.View style={prevAnimatedStyle}>
          <TouchableOpacity
            onPress={handlePreviousPress}
            style={[styles.navButton, styles.secondaryButton]}
            activeOpacity={0.7}
          >
            <IconSymbol
              name="chevron.left"
              size={24}
              color={Colors[colorScheme].text}
            />
          </TouchableOpacity>
        </Animated.View>

        <Animated.View style={autoRotateAnimatedStyle}>
          <TouchableOpacity
            onPress={handleAutoRotatePress}
            style={[
              styles.autoRotateButton,
              isAutoRotating && { backgroundColor: Colors[colorScheme].tint }
            ]}
            activeOpacity={0.7}
          >
            <IconSymbol
              name={isAutoRotating ? "pause.circle.fill" : "play.circle.fill"}
              size={32}
              color={isAutoRotating ? '#FFFFFF' : Colors[colorScheme].tint}
            />
          </TouchableOpacity>
        </Animated.View>

        <Animated.View style={nextAnimatedStyle}>
          <TouchableOpacity
            onPress={handleNextPress}
            style={[styles.navButton, styles.secondaryButton]}
            activeOpacity={0.7}
          >
            <IconSymbol
              name="chevron.right"
              size={24}
              color={Colors[colorScheme].text}
            />
          </TouchableOpacity>
        </Animated.View>
      </View>

      {/* Auto-rotate Status */}
      {isAutoRotating && (
        <View style={styles.statusContainer}>
          <IconSymbol
            name="clock"
            size={16}
            color={Colors[colorScheme].icon}
          />
          <ThemedText style={styles.statusText}>
            Auto-rotating every {formatInterval(autoRotateInterval)}
          </ThemedText>
        </View>
      )}

      {/* Interval Selection */}
      <View style={styles.intervalContainer}>
        <ThemedText style={styles.intervalLabel}>Auto-rotate interval:</ThemedText>
        <View style={styles.intervalButtons}>
          {INTERVAL_OPTIONS.map((interval) => (
            <TouchableOpacity
              key={interval}
              onPress={() => onIntervalChange(interval)}
              style={[
                styles.intervalButton,
                autoRotateInterval === interval && {
                  backgroundColor: Colors[colorScheme].tint,
                }
              ]}
              activeOpacity={0.7}
            >
              <ThemedText
                style={[
                  styles.intervalButtonText,
                  autoRotateInterval === interval && { color: '#FFFFFF' }
                ]}
              >
                {formatInterval(interval)}
              </ThemedText>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    gap: 20,
  },
  navigationControls: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 24,
  },
  navButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  secondaryButton: {
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  autoRotateButton: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
  },
  statusText: {
    fontSize: 14,
    opacity: 0.7,
  },
  intervalContainer: {
    alignItems: 'center',
    gap: 12,
  },
  intervalLabel: {
    fontSize: 14,
    fontWeight: '500',
    opacity: 0.8,
  },
  intervalButtons: {
    flexDirection: 'row',
    gap: 8,
    flexWrap: 'wrap',
    justifyContent: 'center',
  },
  intervalButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
    backgroundColor: 'rgba(0,0,0,0.05)',
    minWidth: 48,
    alignItems: 'center',
  },
  intervalButtonText: {
    fontSize: 12,
    fontWeight: '500',
  },
});
